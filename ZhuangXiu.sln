
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShapesRuntime", "ShapesRuntime.csproj", "{e53a666a-9900-9ceb-3b71-e13e2618cf55}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShapesSamples", "ShapesSamples.csproj", "{0f1115f6-bf75-8016-0e6b-0dd1367a659a}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ShapesEditor", "ShapesEditor.csproj", "{c84b6db9-e331-2d92-62f5-d72e5c504f9c}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{dc41604a-d043-770b-6863-0c3d28c50661}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sirenix.OdinInspector.Modules.UnityMathematics", "Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "{457b567d-55a4-9d68-8cec-8e2dfff27451}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-firstpass", "Assembly-CSharp-firstpass.csproj", "{9bd5cca7-1470-feb4-45cd-666a0a8379b4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-Editor-firstpass", "Assembly-CSharp-Editor-firstpass.csproj", "{4cb59322-f7f0-6b57-9014-bc91b60fa36c}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{e53a666a-9900-9ceb-3b71-e13e2618cf55}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{e53a666a-9900-9ceb-3b71-e13e2618cf55}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0f1115f6-bf75-8016-0e6b-0dd1367a659a}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0f1115f6-bf75-8016-0e6b-0dd1367a659a}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{c84b6db9-e331-2d92-62f5-d72e5c504f9c}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{c84b6db9-e331-2d92-62f5-d72e5c504f9c}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{dc41604a-d043-770b-6863-0c3d28c50661}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{dc41604a-d043-770b-6863-0c3d28c50661}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{457b567d-55a4-9d68-8cec-8e2dfff27451}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{457b567d-55a4-9d68-8cec-8e2dfff27451}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9bd5cca7-1470-feb4-45cd-666a0a8379b4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9bd5cca7-1470-feb4-45cd-666a0a8379b4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4cb59322-f7f0-6b57-9014-bc91b60fa36c}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4cb59322-f7f0-6b57-9014-bc91b60fa36c}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal

//-------------------------------------------------
//			  NGUI: Next-Gen UI kit
// Copyright © 2011-2020 Tasharen Entertainment Inc
//-------------------------------------------------

using UnityEditor;
using UnityEngine;

/// <summary>
/// Tools for the editor
/// </summary>

static public class NGUITools
{
    static public string GetFuncName(object obj, string method)
    {
        if (obj == null) return "<null>";
        string type = obj.GetType().ToString();
        int period = type.LastIndexOf('/');
        if (period > 0) type = type.Substring(period + 1);
        return string.IsNullOrEmpty(method) ? type : type + "/" + method;
    }

    static public void SetDirty(Object obj, string undoName = "last change")
    {
        if (obj)
        {
            EditorUtility.SetDirty(obj);

            if (!AssetDatabase.Contains(obj) && !Application.isPlaying)
            {
                if (obj is Component)
                {
                    var component = (Component)obj;
                    UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(component.gameObject.scene);
                }
                else if (!(obj is EditorWindow || obj is ScriptableObject))
                {
                    UnityEditor.SceneManagement.EditorSceneManager.MarkAllScenesDirty();
                }
            }
        }
    }
}
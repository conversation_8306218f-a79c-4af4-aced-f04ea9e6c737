using System;
using System.Collections.Generic;
using UnityEngine.Events;

[Serializable]
public class EventDelegateCollection
{
    public List<EventDelegate> delegates = new List<EventDelegate>();

    public void RemoveAllListeners()
    {
        delegates.Clear();
    }

    public void AddListener(UnityAction action)
    {
        delegates.Add(new(action));
    }

    public void Invoke()
    {
        EventDelegate.Execute(delegates);
    }
}
using Sirenix.OdinInspector;
using UnityEngine;

public class Ruler : MonoBeh<PERSON>our
{
    public enum Direction
    {
        Up,
        Down,
        Left,
        Right,
    }

    [SerializeField]
    private Transform _reference;

    [SerializeField]
    private Direction _direction;

    [SerializeField]
    private int _length;

    private Vector3 _dirValue => _direction switch
    {
        Direction.Up => Vector3.forward,
        Direction.Down => Vector3.back,
        Direction.Left => Vector3.left,
        Direction.Right => Vector3.right,
    };

    [Button]
    public bool Apply()
    {
        var p = _reference.position + _dirValue * _length;
        if (transform.position != p)
        {
            transform.position = p;
            return true;
        }

        return false;
    }
}
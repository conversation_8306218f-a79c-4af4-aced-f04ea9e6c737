using System;
using System.Collections.Generic;
using UnityEngine;

public class HexBlocks : MonoBehaviour
{
    [SerializeField, Range(0, 359)]
    private int _startAngle = 0;

    [SerializeField]
    private int _radius = 173;

    [SerializeField, Range(0, 5)]
    private int _margin = 2;

    [SerializeField, Range(1, 10)]
    private int _depth = 5;

    [SerializeField]
    private Color _color;

    private Hex _hex = new();

    private int _currentRadius = -1;
    private int _currentMargin = -1;
    private int _currentDepth = -1;
    private Vector3[] _posPoints = new Vector3[6];
    private Vector3[] _stepPoints = new Vector3[6];
    private List<Vector3[]> _currentHexes = new List<Vector3[]>(100);

    private void OnDrawGizmos()
    {
        bool changed = _hex.UpdateStartAngle(_startAngle);

        if (changed || _currentRadius != _radius)
        {
            for (int index = 0; index < 6; index++)
            {
                _posPoints[index] = _hex.Points[index] * _radius;
            }

            _currentRadius = _radius;
            changed = true;
        }

        if (changed || _currentMargin != _margin)
        {
            float stepRate = 2 * _radius + _margin;
            for (int index = 0; index < 6; index++)
            {
                _stepPoints[index] = _hex.Steps[index] * stepRate;
            }

            _currentMargin = _margin;
            changed = true;
        }

        if (changed || _currentDepth != _depth)
        {
            _currentHexes.Clear();

            if (0 < _depth)
            {
                AddHex(Vector3.zero);
            }

            if (1 < _depth)
            {
                for (int depth = 2; depth <= _depth; depth++)
                {
                    AddCircle(depth);
                }
            }

            _currentDepth = _depth;
        }

        var defaultColor = Gizmos.color;
        Gizmos.color = _color;
        for (int index = 0; index < _currentHexes.Count; index++)
        {
            DrawHex(_currentHexes[index]);
        }
        Gizmos.color = defaultColor;
    }

    private void AddHex(Vector3 center)
    {
        var points = new Vector3[6];
        for (int index = 0; index < 6; index++)
        {
            points[index] = center + _posPoints[index];
        }
        _currentHexes.Add(points);
    }

    private void AddCircle(int depth)
    {
        var stepCount = depth - 1;
        if (1 > stepCount) return;

        for (int index = 0; index < 6; index++)
        {
            var center = _stepPoints[index] * stepCount;
            AddHex(center);
            if (1 < stepCount)
            {
                var innerStep = _stepPoints[(index + 2) % 6];
                for (int step = 1; step < stepCount; step++)
                {
                    center += innerStep;
                    AddHex(center);
                }
            }
        }
    }

    private void DrawHex(Vector3[] points)
    {
        var p = transform.position + points[points.Length - 1];
        Vector3 nextP;
        for (int index = 0; index < 6; index++)
        {
            nextP = transform.position + points[index];
            Gizmos.DrawLine(p, nextP);
            p = nextP;
        }
    }
}

public class Hex
{
    private int _startAngle = -1;

    public readonly Vector3[] Points = new Vector3[6];
    public readonly Vector3[] Steps = new Vector3[6];

    public Hex()
    {
        UpdateStartAngle(0);
    }

    public bool UpdateStartAngle(int startAngle)
    {
        var angle = startAngle;
        while (0 > angle) angle += 360;
        angle = angle % 360;

        if (_startAngle == angle) return false;
        _startAngle = angle;

        for (int index = 0; index < 6; index++)
        {
            Points[index] = new Vector3(Mathf.Cos(angle * Mathf.Deg2Rad), 0, Mathf.Sin(angle * Mathf.Deg2Rad));
            angle += 60;
        }

        for (int index = 0; index < 6; index++)
        {
            var p1 = index;
            var p2 = (index + 1) % 6;
            Steps[index] = (Points[p1] + Points[p2]) / 2;
        }

        return true;
    }
}
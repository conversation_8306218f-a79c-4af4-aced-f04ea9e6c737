using Sirenix.OdinInspector;
using System;
using UnityEditor;
using UnityEngine;

public class LineDrawer : MonoBehaviour
{
    [SerializeField]
    private Color _pointColor;

    [SerializeField]
    private Color _lineColor;

    [SerializeField]
    private Color _lengthColor;

    [SerializeField, Range(0, 10)]
    private float _pointSize = 1f;

    [SerializeField, Range(5, 50)]
    private int _lengthSize = 12;

    [SerializeField]
    private Line[] _lines;

    private GUIStyle _textStyle = null;

    private void OnDrawGizmos()
    {
        if (null == _textStyle)
        {
            _textStyle = new(GUI.skin.label);
            _textStyle.alignment = TextAnchor.MiddleCenter;
        }

        _textStyle.fontSize = _lengthSize;
        _textStyle.normal.textColor = _lengthColor;

        if (null == _lines || 1 > _lines.Length) return;

        for (int index = 0; index < _lines.Length; index++)
        {
            var line = _lines[index];
            if (null == line || !line.Valid) continue;

            DrawPoint(line);
            DrawLine(line);
            DrawLength(line);
        }
    }

    private void DrawPoint(Line line)
    {
        var originColor = Gizmos.color;
        Gizmos.color = _pointColor;
        Gizmos.DrawSphere(line.StartPos, _pointSize);
        Gizmos.DrawSphere(line.EndPos, _pointSize);
        Gizmos.color = originColor;
    }

    private void DrawLine(Line line)
    {
        var originColor = Gizmos.color;
        Gizmos.color = _lineColor;
        Gizmos.DrawLine(line.StartPos, line.EndPos);
        Gizmos.color = originColor;
    }

    private void DrawLength(Line line)
    {
        Handles.Label(line.MidPos + line.LengthOffset, line.Length.ToString("F0"), _textStyle);
    }

    [Button]
    public void ApplyRulers()
    {
        var rulers = transform.GetComponentsInChildren<Ruler>();
        if (null == rulers || 1 > rulers.Length) return;

        bool changed;
        do
        {
            changed = false;

            for (int index = 0; index < rulers.Length; index++)
            {
                var ruler = rulers[index];
                changed |= ruler.Apply();
            }
        }
        while (changed);
    }
}

[Serializable]
public class Line
{
    [SerializeField]
    private Transform _start;
    public Vector3 StartPos => _start.position;

    [SerializeField]
    private Transform _end;
    public Vector3 EndPos => _end.position;

    [SerializeField]
    private Vector3 _lengthOffset;
    public Vector3 LengthOffset => _lengthOffset;

    public bool Valid => null != _start && null != _end;

    public Vector3 MidPos => (StartPos + EndPos) / 2;

    public float Length => Vector3.Distance(StartPos, EndPos);
}
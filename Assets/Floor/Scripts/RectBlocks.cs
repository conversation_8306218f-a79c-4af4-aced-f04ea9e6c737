using System;
using System.Collections.Generic;
using UnityEngine;

public class RectBlocks : MonoBehaviour
{
    [SerializeField]
    private int _width = 800;

    [SerializeField]
    private int _height = 400;

    [SerializeField, Range(0, 5)]
    private int _margin = 2;

    [SerializeField, Range(1, 20)]
    private int _wCount = 10;

    [SerializeField, Range(1, 20)]
    private int _hCount = 10;

    [SerializeField]
    private Color _color;

    private Rect _rect = new();

    private int _currentWidth = -1;
    private int _currentHeight = -1;
    private int _currentMargin = -1;
    private int _currentWCount = -1;
    private int _currentHCount = -1;

    private Vector3[] _posPoints = new Vector3[4];
    private Vector3 _xStep, _zStep;
    private List<Vector3[]> _currentRects = new List<Vector3[]>(100);

    private void OnDrawGizmos()
    {
        bool changed = false;

        if (changed || (_currentWidth != _width || _currentHeight != _height))
        {
            for (int index = 0; index < 4; index++)
            {
                var point = _rect.Points[index];
                _posPoints[index] = new Vector3(point.x * _width, 0, point.z * _height);
            }

            _currentWidth = _width;
            _currentHeight = _height;
            changed = true;
        }

        if (changed || _currentMargin != _margin)
        {
            var wStep = 2 * _currentWidth + _margin;
            var hStep = 2 * _currentHeight + _margin;
            _xStep = _rect.XStep * wStep;
            _zStep = _rect.ZStep * hStep;

            _currentMargin = _margin;
            changed = true;
        }

        if (changed || (_currentWCount != _wCount || _currentHCount != _hCount))
        {
            _currentRects.Clear();

            for (int w = 0; w < _wCount; w++)
            {
                var x = w * _xStep;
                for (int h = 0; h < _hCount; h++)
                {
                    var z = h * _zStep;
                    AddRect(x + z);
                }
            }

            _currentWCount = _wCount;
            _currentHCount = _hCount;
        }

        var defaultColor = Gizmos.color;
        Gizmos.color = _color;
        for (int index = 0; index < _currentRects.Count; index++)
        {
            DrawRect(_currentRects[index]);
        }
        Gizmos.color = defaultColor;
    }

    private void AddRect(Vector3 center)
    {
        var points = new Vector3[4];
        for (int index = 0; index < 4; index++)
        {
            points[index] = center + _posPoints[index];
        }
        _currentRects.Add(points);
    }

    private void DrawRect(Vector3[] points)
    {
        var p = transform.position + points[points.Length - 1];
        Vector3 nextP;
        for (int index = 0; index < 4; index++)
        {
            nextP = transform.position + points[index];
            Gizmos.DrawLine(p, nextP);
            p = nextP;
        }
    }
}

public class Rect
{
    public readonly Vector3[] Points = new Vector3[4]
    {
        new Vector3( 0.5f, 0, -0.5f),
        new Vector3(-0.5f, 0, -0.5f),
        new Vector3(-0.5f, 0,  0.5f),
        new Vector3( 0.5f, 0,  0.5f),
    };

    public readonly Vector3 XStep = new Vector3(0.5f, 0, 0);
    public readonly Vector3 ZStep = new Vector3(0, 0, 0.5f);
}